import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/services/package_data_service.dart';
import 'package:rideoon/services/address_service.dart';
import 'package:rideoon/services/auth_service.dart';
import 'package:rideoon/models/address/address_request.dart';
import 'package:rideoon/models/address/address.dart';
import 'package:rideoon/models/api_response.dart';
import 'package:rideoon/views/dashboards/user_dashboard/send_a_package/location_confirmation_map.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'package:rideoon/views/custom_widgets/push_notfication.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Receiver Details screen for package delivery
///
/// This screen allows users to input receiver information including
/// name, delivery method, destination state, address, and phone number.
class ReceiverDetailsView extends StatefulWidget {
  const ReceiverDetailsView({super.key});

  @override
  State<ReceiverDetailsView> createState() => _ReceiverDetailsViewState();
}

class _ReceiverDetailsViewState extends State<ReceiverDetailsView> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _addressController = TextEditingController();
  final _phoneController = TextEditingController();

  String? _selectedState;
  bool _isLoading = false;
  LatLng? _confirmedLocation;
  List<Map<String, dynamic>> _locationHistory = [];
  List<Map<String, dynamic>> _savedAddresses = [];
  bool _hasUnsavedChanges = false;
  String? _originalAddress;
  String? _selectedAddressUuid; // Track UUID of selected address for editing

  // Nigerian states for dropdown
  final List<String> _nigerianStates = [
    'Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', 'Bauchi', 'Bayelsa', 'Benue',
    'Borno', 'Cross River', 'Delta', 'Ebonyi', 'Edo', 'Ekiti', 'Enugu',
    'FCT - Abuja', 'Gombe', 'Imo', 'Jigawa', 'Kaduna', 'Kano', 'Katsina',
    'Kebbi', 'Kogi', 'Kwara', 'Lagos', 'Nasarawa', 'Niger', 'Ogun', 'Ondo',
    'Osun', 'Oyo', 'Plateau', 'Rivers', 'Sokoto', 'Taraba', 'Yobe', 'Zamfara'
  ];



  @override
  void initState() {
    super.initState();
    _loadSavedData();
    _loadLocationHistory();
    _loadSavedAddresses();
    _setupTextFieldListeners();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  /// Load saved receiver data if available
  Future<void> _loadSavedData() async {
    final data = await PackageDataService.getReceiverData();
    if (data != null) {
      setState(() {
        _nameController.text = data['name'] ?? '';
        _addressController.text = data['address'] ?? '';
        _phoneController.text = data['phone'] ?? '';
        _selectedState = data['state'];
        _originalAddress = data['address'] ?? '';

        // Load confirmed location if available
        if (data['latitude'] != null && data['longitude'] != null) {
          _confirmedLocation = LatLng(data['latitude'], data['longitude']);
        }
      });
    }
  }

  /// Load saved addresses
  Future<void> _loadSavedAddresses() async {
    try {
      // Get auth token from remember me credentials
      final credentials = await AuthService.getRememberMeCredentials();
      String? authToken;
      if (credentials != null) {
        // For demo purposes, we'll use a placeholder token
        // In production, you'd get this from your authentication flow
        authToken = 'user_auth_token_placeholder';
      }

      // Use the legacy method for backward compatibility
      final addresses = await AddressService.getSavedAddresses(authToken: authToken);

      // Filter for receiver addresses (or addresses that can be used for receiver)
      final receiverAddresses = addresses.where((addr) =>
        addr['type'] == 'receiver' || addr['type'] == 'address'
      ).toList();

      setState(() {
        _savedAddresses = receiverAddresses;
      });
    } catch (e) {
      // Silently handle errors - don't break the UI
      setState(() {
        _savedAddresses = [];
      });
    }
  }

  /// Setup text field listeners to detect changes
  void _setupTextFieldListeners() {
    _addressController.addListener(() {
      final currentAddress = _addressController.text.trim();
      if (currentAddress != _originalAddress) {
        setState(() {
          _hasUnsavedChanges = true;
          _confirmedLocation = null; // Reset location confirmation
        });
      }
    });
  }

  /// Load location history
  Future<void> _loadLocationHistory() async {
    final history = await PackageDataService.getReceiverLocationHistory();
    setState(() {
      _locationHistory = history;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      resizeToAvoidBottomInset: true,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: _getSpacing(context, 33),
                vertical: _getSpacing(context, 16),
              ),
              child: _buildHeader(context),
            ),

            // Divider
            _buildDivider(context),

            // Form content with keyboard-aware layout
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: _getSpacing(context, 33)),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: _getSpacing(context, 32)),

                      // Receiver Name
                      _buildLabeledField(
                        label: 'Receiver Name',
                        child: _buildTextFormField(
                          controller: _nameController,
                          hintText: 'Enter receiver name',
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter receiver name';
                            }
                            return null;
                          },
                        ),
                      ),

                      SizedBox(height: _getSpacing(context, 20)),

                      // Destination State
                      _buildLabeledField(
                        label: 'Destination State',
                        child: _buildStateSelector(context),
                      ),

                      SizedBox(height: _getSpacing(context, 20)),

                      // Address
                      _buildLabeledField(
                        label: 'Address',
                        child: _buildTextFormField(
                          controller: _addressController,
                          hintText: 'Enter address',
                          maxLines: 2,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter address';
                            }
                            return null;
                          },
                        ),
                      ),

                      SizedBox(height: _getSpacing(context, 12)),

                      // Confirm Location Button
                      _buildConfirmLocationButton(context),

                      SizedBox(height: _getSpacing(context, 20)),

                      // Saved Addresses (if available)
                      if (_savedAddresses.isNotEmpty) ...[
                        _buildSavedAddresses(context),
                        SizedBox(height: _getSpacing(context, 20)),
                      ],

                      // Location History (if available)
                      if (_locationHistory.isNotEmpty) ...[
                        _buildLocationHistory(context),
                        SizedBox(height: _getSpacing(context, 20)),
                      ],

                      // Phone Number
                      _buildLabeledField(
                        label: 'Phone Number',
                        child: _buildPhoneNumberField(context),
                      ),

                      SizedBox(height: _getSpacing(context, 32)),

                      // Continue Button (now inside scrollable area)
                      _buildContinueButton(context),

                      // Bottom padding to ensure button is visible above keyboard
                      SizedBox(height: MediaQuery.of(context).viewInsets.bottom + _getSpacing(context, 24)),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        GestureDetector(
          onTap: () => Navigator.of(context).pop(),
          child: Container(
            width: _getIconSize(context) + 8,
            height: _getIconSize(context) + 8,
            decoration: BoxDecoration(
              color: AppColors.black.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(_getBorderRadius(context)),
            ),
            child: Icon(
              Icons.arrow_back_ios_new,
              size: _getIconSize(context),
              color: AppColors.black,
            ),
          ),
        ),
        Expanded(
          child: Center(
            child: Text(
              'Receiver Details',
              style: TextStyle(
                color: AppColors.black,
                fontSize: _getHeadingFontSize(context),
                fontFamily: 'Bricolage Grotesque',
                fontWeight: FontWeight.w600,
                letterSpacing: -0.8,
              ),
            ),
          ),
        ),
        SizedBox(width: _getIconSize(context) + 8), // Balance the back button
      ],
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getSpacing(context, 33)),
      child: Container(
        width: double.infinity,
        decoration: ShapeDecoration(
          shape: RoundedRectangleBorder(
            side: BorderSide(
              width: 1,
              strokeAlign: BorderSide.strokeAlignCenter,
              color: AppColors.black.withValues(alpha: 0.1),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLabeledField({
    required String label,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: AppColors.black,
            fontSize: 14,
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        child,
      ],
    );
  }

  Widget _buildTextFormField({
    required TextEditingController controller,
    required String hintText,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0x0A1E1E1E),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.02),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        maxLines: maxLines,
        validator: validator,
        style: TextStyle(
          color: Colors.black.withValues(alpha: 0.88),
          fontSize: 14,
          fontFamily: 'Inter',
          fontWeight: FontWeight.w400,
        ),
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: TextStyle(
            color: Colors.black.withValues(alpha: 0.37),
            fontSize: 14,
            fontFamily: 'Inter',
            fontWeight: FontWeight.w400,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
        ),
      ),
    );
  }



  Widget _buildStateSelector(BuildContext context) {
    return GestureDetector(
      onTap: () => _showStateBottomSheet(context),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: const Color(0x0A1E1E1E),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.02),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _selectedState ?? 'Select destination state',
                style: TextStyle(
                  color: _selectedState != null
                      ? Colors.black.withValues(alpha: 0.88)
                      : Colors.black.withValues(alpha: 0.37),
                  fontSize: 14,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                ),
              ),
              Icon(
                Icons.keyboard_arrow_down,
                color: Colors.black.withValues(alpha: 0.37),
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPhoneNumberField(BuildContext context) {
    return Row(
      children: [
        // Country code container
        Container(
          width: 80,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0x0A1E1E1E),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.02),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            child: Text(
              '+234',
              style: TextStyle(
                color: Colors.black.withValues(alpha: 0.88),
                fontSize: 14,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),

        const SizedBox(width: 12),

        // Phone number field
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0x0A1E1E1E),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.02),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextFormField(
              controller: _phoneController,
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter phone number';
                }
                if (value.length < 10) {
                  return 'Please enter a valid phone number';
                }
                return null;
              },
              style: TextStyle(
                color: Colors.black.withValues(alpha: 0.88),
                fontSize: 14,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
              ),
              decoration: InputDecoration(
                hintText: 'Enter phone number',
                hintStyle: TextStyle(
                  color: Colors.black.withValues(alpha: 0.37),
                  fontSize: 14,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContinueButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleContinue,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
        child: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                'Continue',
                style: TextStyle(
                  fontSize: 16,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  Widget _buildConfirmLocationButton(BuildContext context) {
    final hasAddress = _addressController.text.trim().isNotEmpty;

    return Container(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: hasAddress ? () => _confirmLocationOnMap() : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: _confirmedLocation != null
              ? AppColors.success
              : AppColors.primary.withValues(alpha: hasAddress ? 1.0 : 0.5),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
        icon: Icon(
          _confirmedLocation != null ? Icons.check_circle : Icons.location_on,
          size: 20,
        ),
        label: Text(
          _confirmedLocation != null && !_hasUnsavedChanges
              ? 'Location Confirmed'
              : 'Confirm location on map',
          style: TextStyle(
            fontSize: 14,
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildSavedAddresses(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Saved Receiver Addresses',
          style: TextStyle(
            color: AppColors.black,
            fontSize: 14,
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          constraints: BoxConstraints(maxHeight: 150),
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _savedAddresses.length > 3 ? 3 : _savedAddresses.length,
            itemBuilder: (context, index) {
              final address = _savedAddresses[index];
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListTile(
                  dense: true,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  leading: Icon(
                    Icons.bookmark,
                    size: 20,
                    color: AppColors.primary,
                  ),
                  title: Text(
                    address['name'] ?? address['addressName'] ?? 'Saved Address',
                    style: TextStyle(
                      fontSize: 12,
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                      color: AppColors.black.withValues(alpha: 0.9),
                    ),
                  ),
                  subtitle: Text(
                    address['street'] ?? address['address'] ?? address['fullAddress'] ?? '',
                    style: TextStyle(
                      fontSize: 11,
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      color: AppColors.black.withValues(alpha: 0.7),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  onTap: () {
                    setState(() {
                      // Use the correct field names from API response
                      _nameController.text = address['name'] ?? address['addressName'] ?? '';
                      _addressController.text = address['street'] ?? address['address'] ?? address['fullAddress'] ?? '';
                      _phoneController.text = address['phoneNumber']?.toString() ?? address['phone'] ?? '';
                      _selectedState = address['state'] ?? address['city'];

                      // Parse coordinates
                      if (address['latitude'] != null && address['longitude'] != null) {
                        final lat = double.tryParse(address['latitude'].toString());
                        final lng = double.tryParse(address['longitude'].toString());
                        if (lat != null && lng != null) {
                          _confirmedLocation = LatLng(lat, lng);
                        }
                      }

                      _originalAddress = address['street'] ?? address['address'] ?? address['fullAddress'] ?? '';
                      _selectedAddressUuid = address['id']; // Store UUID for editing
                      _hasUnsavedChanges = false;
                    });

                    // Mark as recently used (legacy method - no-op in API version)
                    AddressService.markAsRecentlyUsed(address['id'] ?? '');
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLocationHistory(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Locations',
          style: TextStyle(
            color: AppColors.black,
            fontSize: 14,
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          constraints: BoxConstraints(maxHeight: 150),
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _locationHistory.length > 3 ? 3 : _locationHistory.length,
            itemBuilder: (context, index) {
              final location = _locationHistory[index];
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListTile(
                  dense: true,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  leading: Icon(
                    Icons.history,
                    size: 20,
                    color: AppColors.black.withValues(alpha: 0.6),
                  ),
                  title: Text(
                    location['address'] ?? '',
                    style: TextStyle(
                      fontSize: 12,
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      color: AppColors.black.withValues(alpha: 0.8),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  onTap: () {
                    setState(() {
                      _addressController.text = location['address'] ?? '';
                      if (location['latitude'] != null && location['longitude'] != null) {
                        _confirmedLocation = LatLng(location['latitude'], location['longitude']);
                      }
                      _hasUnsavedChanges = true;
                    });
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Future<void> _confirmLocationOnMap() async {
    final address = _addressController.text.trim();
    if (address.isEmpty) return;

    final result = await Navigator.of(context).push<Map<String, dynamic>>(
      MaterialPageRoute(
        builder: (context) => LocationConfirmationMap(
          address: address,
          title: 'Delivery Location',
          initialCoordinates: _confirmedLocation,
        ),
      ),
    );

    if (result != null) {
      setState(() {
        _confirmedLocation = result['coordinates'];
        _addressController.text = result['address'];
        _originalAddress = result['address'];
        _hasUnsavedChanges = false; // Reset since location is now confirmed
      });

      // Save to location history
      await PackageDataService.saveReceiverLocationToHistory({
        'address': result['address'],
        'latitude': result['latitude'],
        'longitude': result['longitude'],
      });

      // Reload location history
      await _loadLocationHistory();
    }
  }

  void _showStateBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Text(
                'Select Destination State',
                style: TextStyle(
                  color: AppColors.black,
                  fontSize: 18,
                  fontFamily: 'Bricolage Grotesque',
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),

            // States list
            Expanded(
              child: ListView.builder(
                itemCount: _nigerianStates.length,
                itemBuilder: (context, index) {
                  final state = _nigerianStates[index];
                  return ListTile(
                    title: Text(
                      state,
                      style: TextStyle(
                        color: AppColors.black,
                        fontSize: 16,
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    onTap: () {
                      setState(() {
                        _selectedState = state;
                      });
                      Navigator.of(context).pop();
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleContinue() async {
    if (_formKey.currentState!.validate()) {
      if (_selectedState == null) {
        Toast.error('Please select a destination state');
        return;
      }

      setState(() {
        _isLoading = true;
      });

      try {
        // Save receiver data to local storage
        final receiverData = {
          'name': _nameController.text.trim(),
          'address': _addressController.text.trim(),
          'phone': _phoneController.text.trim(),
          'state': _selectedState,
          'latitude': _confirmedLocation?.latitude,
          'longitude': _confirmedLocation?.longitude,
        };

        await PackageDataService.saveReceiverData(receiverData);

        // Show save address dialog if this is a new/modified address
        if (_hasUnsavedChanges || _originalAddress != _addressController.text.trim()) {
          await _showSaveAddressDialog(receiverData);
        }

        // Show success toast
        Toast.success('Receiver details saved successfully');

        // Navigate back to send package view
        if (mounted) {
          Navigator.of(context).pop(true); // Return true to indicate success
        }
      } catch (e) {
        Toast.error('Failed to save receiver details: $e');
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Show dialog to ask user if they want to save the address
  Future<Future<bool?>> _showSaveAddressDialog(Map<String, dynamic> receiverData) async {
    return PushNotificationDialog.show(
      context,
      title: 'Save Address',
      description: 'Do you want to save this address for later use?',
      acceptButtonText: 'Yes, Save',
      declineButtonText: 'No, Thanks',
      icon: Icons.bookmark_add,
      iconBackgroundColor: AppColors.primary,
      iconColor: AppColors.primary,
      onAccept: () async {
        try {
          // Create AddressRequest from receiver data
          final addressRequest = AddressRequest(
            name: receiverData['name'] ?? 'Unknown',
            phoneNumber: int.tryParse(receiverData['phone']?.toString().replaceAll(RegExp(r'[^\d]'), '') ?? '0') ?? 0,
            street: '${receiverData['address']} ${receiverData['landmark'] ?? ''}'.trim(),
            city: receiverData['state'] ?? '',
            state: receiverData['state'] ?? '',
            country: 'Nigeria',
            longitude: receiverData['longitude'] != null ? double.tryParse(receiverData['longitude'].toString()) : null,
            latitude: receiverData['latitude'] != null ? double.tryParse(receiverData['latitude'].toString()) : null,
          );

          // Get auth token from remember me credentials
          final credentials = await AuthService.getRememberMeCredentials();
          String? authToken;
          if (credentials != null) {
            // For demo purposes, we'll use a placeholder token
            // In production, you'd get this from your authentication flow
            authToken = 'user_auth_token_placeholder';
          }

          // Check if we're editing an existing address or creating a new one
          late final ApiResponse<Address> response;
          if (_selectedAddressUuid != null && _selectedAddressUuid!.isNotEmpty) {
            // Update existing address
            response = await AddressService.updateAddress(_selectedAddressUuid!, addressRequest, authToken: authToken);
          } else {
            // Create new address
            response = await AddressService.createAddress(addressRequest, authToken: authToken);
          }

          if (response.success) {
            await _loadSavedAddresses(); // Refresh saved addresses
            final action = _selectedAddressUuid != null ? 'updated' : 'saved';
            Toast.success('Address $action successfully');
          } else {
            Toast.error('Failed to save address: ${response.message}');
          }
        } catch (e) {
          Toast.error('Failed to save address: $e');
        }
      },
      onDecline: () {
        // Dialog will close automatically
      },
    );
  }

  // Helper methods for responsive design
  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 350) return baseSpacing * 0.8;
    if (screenWidth > 600) return baseSpacing * 1.2;
    return baseSpacing;
  }

  double _getIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 350) return 18;
    if (screenWidth > 600) return 24;
    return 20;
  }

  double _getBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 350) return 6;
    if (screenWidth > 600) return 10;
    return 8;
  }

  double _getHeadingFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 350) return 18;
    if (screenWidth > 600) return 24;
    return 20;
  }
}