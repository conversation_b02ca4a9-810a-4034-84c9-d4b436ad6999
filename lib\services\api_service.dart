import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:rideoon/services/config_service.dart';
import 'package:rideoon/models/api_response.dart';

/// Centralized API service for handling all HTTP requests
///
/// This service provides a unified interface for making API calls with:
/// - Automatic header management
/// - Authentication token handling
/// - Error handling and response parsing
/// - Request/response logging in debug mode
/// - Timeout management
class ApiService {
  static final String _baseUrl = ConfigService.apiBaseUrl;
  static final int _timeout = ConfigService.apiTimeout;

  /// Make an authenticated API request
  ///
  /// [endpoint] - The API endpoint (e.g., '/v1/client/sign-up')
  /// [method] - HTTP method (GET, POST, PUT, PATCH, DELETE)
  /// [body] - Request body for POST/PUT/PATCH requests
  /// [queryParams] - Query parameters for GET requests
  /// [requiresAuth] - Whether the request requires authentication token
  /// [customHeaders] - Additional headers to include
  static Future<ApiResponse<T>> makeRequest<T>({
    required String endpoint,
    required String method,
    Map<String, dynamic>? body,
    Map<String, String>? queryParams,
    bool requiresAuth = true,
    Map<String, String>? customHeaders,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      // Build URI with query parameters
      Uri uri = Uri.parse('$_baseUrl$endpoint');
      if (queryParams != null && queryParams.isNotEmpty) {
        uri = uri.replace(queryParameters: queryParams);
      }

      // Build headers
      final headers = await _buildHeaders(
        requiresAuth: requiresAuth,
        customHeaders: customHeaders,
      );

      if (ConfigService.enableDebugMode) {
        print('ApiService: Making ${method.toUpperCase()} request to $uri');
        if (body != null) {
          print('ApiService: Request body: ${jsonEncode(body)}');
        }
        print('ApiService: Headers: $headers');
      }

      // Make HTTP request
      final response = await _makeHttpRequest(
        uri: uri,
        method: method,
        headers: headers,
        body: body,
      );

      if (ConfigService.enableDebugMode) {
        print('ApiService: Response status: ${response.statusCode}');
        print('ApiService: Response body: ${response.body}');
      }

      // Parse response
      return _parseResponse<T>(response, fromJson);

    } on SocketException {
      return ApiResponse.error(
        message: 'No internet connection. Please check your network and try again.',
        statusCode: 0,
      );
    } on TimeoutException {
      return ApiResponse.error(
        message: 'Request timeout. Please try again.',
        statusCode: 408,
      );
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('ApiService: Unexpected error: $e');
      }
      return ApiResponse.error(
        message: 'An unexpected error occurred. Please try again.',
        statusCode: 500,
      );
    }
  }

  /// Build headers for API requests
  static Future<Map<String, String>> _buildHeaders({
    bool requiresAuth = true,
    Map<String, String>? customHeaders,
  }) async {
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': '${ConfigService.appName}/${ConfigService.appVersion}',
      'x-request-referral': 'riideon', // Required header for API
    };

    // Add authentication token if required
    if (requiresAuth) {
      final token = await getAuthToken();
      if (token != null && token.isNotEmpty) {
        headers['Authorization'] = 'Bearer $token';
      }
    }

    // Add API key if available
    final apiKey = ConfigService.apiKey;
    if (apiKey.isNotEmpty && apiKey != 'dev_api_key_placeholder') {
      headers['Authorization'] = 'Bearer $apiKey';
    }

    // Add custom headers
    if (customHeaders != null) {
      headers.addAll(customHeaders);
    }

    return headers;
  }

  /// Make HTTP request based on method
  static Future<http.Response> _makeHttpRequest({
    required Uri uri,
    required String method,
    required Map<String, String> headers,
    Map<String, dynamic>? body,
  }) async {
    final bodyString = body != null ? jsonEncode(body) : null;

    switch (method.toUpperCase()) {
      case 'GET':
        return await http.get(uri, headers: headers)
            .timeout(Duration(milliseconds: _timeout));
      case 'POST':
        return await http.post(
          uri,
          headers: headers,
          body: bodyString,
        ).timeout(Duration(milliseconds: _timeout));
      case 'PUT':
        return await http.put(
          uri,
          headers: headers,
          body: bodyString,
        ).timeout(Duration(milliseconds: _timeout));
      case 'PATCH':
        return await http.patch(
          uri,
          headers: headers,
          body: bodyString,
        ).timeout(Duration(milliseconds: _timeout));
      case 'DELETE':
        return await http.delete(uri, headers: headers)
            .timeout(Duration(milliseconds: _timeout));
      default:
        throw Exception('Unsupported HTTP method: $method');
    }
  }

  /// Parse HTTP response into ApiResponse
  static ApiResponse<T> _parseResponse<T>(
    http.Response response,
    T Function(Map<String, dynamic>)? fromJson,
  ) {
    try {
      // Parse response body
      Map<String, dynamic> responseData;
      try {
        responseData = jsonDecode(response.body) as Map<String, dynamic>;
      } catch (e) {
        return ApiResponse.error(
          message: 'Invalid JSON response from server',
          statusCode: response.statusCode,
        );
      }

      // Handle successful response (2xx status codes)
      if (response.statusCode >= 200 && response.statusCode < 300) {
        T? data;
        if (fromJson != null && responseData.containsKey('data')) {
          try {
            data = fromJson(responseData['data'] as Map<String, dynamic>);
          } catch (e) {
            if (ConfigService.enableDebugMode) {
              print('ApiService: Error parsing response data: $e');
            }
          }
        }

        return ApiResponse.success(
          message: responseData['message'] as String? ?? 'Success',
          data: data,
          statusCode: response.statusCode,
        );
      }

      // Handle error responses
      String errorMessage = 'An error occurred';
      Map<String, dynamic>? errors;

      if (responseData.containsKey('message')) {
        errorMessage = responseData['message'] as String;
      }

      if (responseData.containsKey('errors')) {
        errors = responseData['errors'] as Map<String, dynamic>?;
      }

      return ApiResponse.error(
        message: errorMessage,
        statusCode: response.statusCode,
        errors: errors,
      );

    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to parse server response',
        statusCode: response.statusCode,
      );
    }
  }

  /// Get stored authentication token
  static Future<String?> getAuthToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('auth_token');
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('ApiService: Error getting auth token: $e');
      }
      return null;
    }
  }

  /// Check if API is configured properly
  static bool get isConfigured {
    return ConfigService.hasValue('API_BASE_URL') &&
           ConfigService.hasValue('API_KEY');
  }

  /// Get API configuration info (for debugging)
  static Map<String, dynamic> getConfigInfo() {
    if (!ConfigService.isDevelopment) {
      throw Exception('Config info can only be accessed in development mode');
    }

    return {
      'baseUrl': _baseUrl,
      'timeout': _timeout,
      'hasApiKey': ConfigService.hasValue('API_KEY'),
      'environment': ConfigService.appEnvironment,
      'isConfigured': isConfigured,
    };
  }
}
