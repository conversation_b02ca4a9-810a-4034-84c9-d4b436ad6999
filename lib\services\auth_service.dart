import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:rideoon/services/config_service.dart';
import 'package:rideoon/models/auth/sign_up_request.dart';
import 'package:rideoon/models/auth/sign_up_response.dart';
import 'package:rideoon/models/auth/sign_in_request.dart';
import 'package:rideoon/models/auth/sign_in_response.dart';
import 'package:rideoon/models/auth/account.dart';
import 'package:rideoon/models/api_response.dart';

/// Service class for handling authentication-related API calls
class AuthService {
  static final String _baseUrl = ConfigService.apiBaseUrl;
  static final int _timeout = ConfigService.apiTimeout;

  /// Sign up a new user
  /// 
  /// Takes a [SignUpRequest] and returns an [ApiResponse<SignUpResponse>]
  /// The API endpoint is /v1/client/sign-up
  static Future<ApiResponse<SignUpResponse>> signUp(SignUpRequest request) async {
    try {
      final uri = Uri.parse('$_baseUrl/v1/client/sign-up');
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': '${ConfigService.appName}/${ConfigService.appVersion}',
        'x-request-referral': 'riideon', // Required header - must match API key
      };

      // Add authorization header based on Swagger "Authorize" field
      final apiKey = ConfigService.apiKey;
      if (apiKey.isNotEmpty && apiKey != 'dev_api_key_placeholder') {
        // Based on curl example and Swagger authorize field
        headers['Authorization'] = 'Bearer $apiKey';
      }

      if (ConfigService.enableDebugMode) {
        print('AuthService: Making sign-up request to $uri');
        print('AuthService: Request body: ${jsonEncode(request.toJson())}');
      }

      final response = await http.post(
        uri,
        headers: headers,
        body: jsonEncode(request.toJson()),
      ).timeout(Duration(milliseconds: _timeout));

      if (ConfigService.enableDebugMode) {
        print('AuthService: Response status: ${response.statusCode}');
        print('AuthService: Response body: ${response.body}');
      }

      // Check if response is HTML (common when API is behind a proxy/firewall)
      if (response.body.trim().startsWith('<!DOCTYPE html>') ||
          response.body.trim().startsWith('<html')) {

        // Extract meaningful message from HTML if possible
        String errorMessage = 'Server returned HTML instead of JSON';
        if (response.body.contains('missed the magic word')) {
          errorMessage = 'API authentication failed - missing required parameters';
        } else if (response.body.contains('Powered by Greybox')) {
          errorMessage = 'API endpoint may be behind a proxy or firewall';
        }

        return ApiResponse.error(
          message: errorMessage,
          statusCode: response.statusCode,
        );
      }

      // Parse response body
      Map<String, dynamic> responseData;
      try {
        responseData = jsonDecode(response.body) as Map<String, dynamic>;
      } catch (e) {
        return ApiResponse.error(
          message: 'Invalid JSON response from server: ${e.toString()}',
          statusCode: response.statusCode,
        );
      }

      // Handle successful response (2xx status codes)
      if (response.statusCode >= 200 && response.statusCode < 300) {
        // Handle the API's response format which includes status field
        String successMessage = 'Sign up successful!';

        // Check if response has a message field
        if (responseData.containsKey('message')) {
          successMessage = responseData['message'] as String;
        } else if (response.statusCode == 201) {
          successMessage = 'Account created successfully!';
        }

        // Create a SignUpResponse from the API data
        final signUpResponse = SignUpResponse(
          success: true,
          message: successMessage,
          userId: responseData['id'] as String?,
          token: responseData['token'] as String?,
          userData: responseData,
        );

        return ApiResponse.success(
          message: successMessage,
          data: signUpResponse,
          statusCode: response.statusCode,
        );
      }
      // Handle client errors (4xx status codes)
      else if (response.statusCode >= 400 && response.statusCode < 500) {
        final errorMessage = responseData['message'] as String? ?? 
                           responseData['error'] as String? ?? 
                           'Sign up failed. Please check your information.';
        
        return ApiResponse.error(
          message: errorMessage,
          statusCode: response.statusCode,
          errors: responseData['errors'] as Map<String, dynamic>?,
        );
      }
      // Handle server errors (5xx status codes)
      else {
        return ApiResponse.error(
          message: 'Server error. Please try again later.',
          statusCode: response.statusCode,
        );
      }
    } on SocketException {
      return ApiResponse.error(
        message: 'No internet connection. Please check your network.',
      );
    } on HttpException catch (e) {
      return ApiResponse.error(
        message: 'Network error: ${e.message}',
      );
    } on FormatException {
      return ApiResponse.error(
        message: 'Invalid response format from server',
      );
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Unexpected error: $e');
      }
      return ApiResponse.error(
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Sign in a user (client or delivery partner)
  ///
  /// Takes a [SignInRequest] and returns an [ApiResponse<SignInResponse>]
  /// The API endpoint is /v1/user/sign-in
  static Future<ApiResponse<SignInResponse>> signIn(SignInRequest request) async {
    try {
      final uri = Uri.parse('$_baseUrl/v1/user/sign-in');
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': '${ConfigService.appName}/${ConfigService.appVersion}',
        'x-request-referral': 'riideon', // Required header - must match API key
      };

      // Add authorization header based on Swagger "Authorize" field
      final apiKey = ConfigService.apiKey;
      if (apiKey.isNotEmpty && apiKey != 'dev_api_key_placeholder') {
        // Based on curl example and Swagger authorize field
        headers['Authorization'] = 'Bearer $apiKey';
      }

      if (ConfigService.enableDebugMode) {
        print('AuthService: Making sign-in request to $uri');
        print('AuthService: Request body: ${jsonEncode(request.toJson())}');
      }

      final response = await http.post(
        uri,
        headers: headers,
        body: jsonEncode(request.toJson()),
      ).timeout(Duration(milliseconds: _timeout));

      if (ConfigService.enableDebugMode) {
        print('AuthService: Response status: ${response.statusCode}');
        print('AuthService: Response body: ${response.body}');
      }

      // Check if response is HTML (common when API is behind a proxy/firewall)
      if (response.body.trim().startsWith('<!DOCTYPE html>') ||
          response.body.trim().startsWith('<html')) {

        // Extract meaningful message from HTML if possible
        String errorMessage = 'Server returned HTML instead of JSON';
        if (response.body.contains('missed the magic word')) {
          errorMessage = 'API authentication failed - missing required parameters';
        } else if (response.body.contains('Powered by Greybox')) {
          errorMessage = 'API endpoint may be behind a proxy or firewall';
        }

        return ApiResponse.error(
          message: errorMessage,
          statusCode: response.statusCode,
        );
      }

      // Parse response body
      Map<String, dynamic> responseData;
      try {
        responseData = jsonDecode(response.body) as Map<String, dynamic>;
      } catch (e) {
        return ApiResponse.error(
          message: 'Invalid JSON response from server: ${e.toString()}',
          statusCode: response.statusCode,
        );
      }

      // Handle successful response (2xx status codes)
      if (response.statusCode >= 200 && response.statusCode < 300) {
        final signInResponse = SignInResponse.fromJson(responseData);

        // Store authentication data
        await _storeAuthData(signInResponse.token, signInResponse.account);

        return ApiResponse.success(
          message: 'Sign in successful!',
          data: signInResponse,
          statusCode: response.statusCode,
        );
      }
      // Handle client errors (4xx status codes)
      else if (response.statusCode >= 400 && response.statusCode < 500) {
        // Special handling for 403 with account verification info
        if (response.statusCode == 403 && responseData.containsKey('account')) {
          final accountData = responseData['account'] as Map<String, dynamic>;
          final isVerified = accountData['verified'] as bool? ?? false;

          if (!isVerified) {
            return ApiResponse.error(
              message: 'Your account is not verified yet. Please check your email for verification instructions.',
              statusCode: response.statusCode,
              errors: {'verification': 'Account pending verification'},
            );
          }
        }

        final errorMessage = responseData['message'] as String? ??
                           responseData['error'] as String? ??
                           'Sign in failed. Please check your credentials.';

        return ApiResponse.error(
          message: errorMessage,
          statusCode: response.statusCode,
          errors: responseData['errors'] as Map<String, dynamic>?,
        );
      }
      // Handle server errors (5xx status codes)
      else {
        return ApiResponse.error(
          message: 'Server error. Please try again later.',
          statusCode: response.statusCode,
        );
      }
    } on SocketException {
      return ApiResponse.error(
        message: 'No internet connection. Please check your network.',
      );
    } on HttpException catch (e) {
      return ApiResponse.error(
        message: 'Network error: ${e.message}',
      );
    } on FormatException {
      return ApiResponse.error(
        message: 'Invalid response format from server',
      );
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Unexpected error: $e');
      }
      return ApiResponse.error(
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Store authentication data locally
  static Future<void> _storeAuthData(String token, Account account) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('auth_token', token);
      await prefs.setString('user_account', jsonEncode(account.toJson()));
      await prefs.setBool('is_logged_in', true);
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Error storing auth data: $e');
      }
    }
  }

  /// Get stored authentication token
  static Future<String?> getAuthToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('auth_token');
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Error getting auth token: $e');
      }
      return null;
    }
  }

  /// Get stored user account
  static Future<Account?> getUserAccount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final accountJson = prefs.getString('user_account');
      if (accountJson != null) {
        final accountData = jsonDecode(accountJson) as Map<String, dynamic>;
        return Account.fromJson(accountData);
      }
      return null;
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Error getting user account: $e');
      }
      return null;
    }
  }

  /// Check if user is logged in
  static Future<bool> isLoggedIn() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('is_logged_in') ?? false;
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Error checking login status: $e');
      }
      return false;
    }
  }

  /// Sign out user (clear stored data)
  static Future<void> signOut() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('auth_token');
      await prefs.remove('user_account');
      await prefs.setBool('is_logged_in', false);
      // Also clear remember me credentials on sign out
      await clearRememberMeCredentials();
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Error signing out: $e');
      }
    }
  }

  /// Store "Remember me" credentials
  static Future<void> saveRememberMeCredentials(String email, String password) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('remember_me_email', email);
      await prefs.setString('remember_me_password', password);
      await prefs.setBool('remember_me_enabled', true);
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Error saving remember me credentials: $e');
      }
    }
  }

  /// Get saved "Remember me" credentials
  static Future<Map<String, String>?> getRememberMeCredentials() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isEnabled = prefs.getBool('remember_me_enabled') ?? false;

      if (isEnabled) {
        final email = prefs.getString('remember_me_email');
        final password = prefs.getString('remember_me_password');

        if (email != null && password != null) {
          return {
            'email': email,
            'password': password,
          };
        }
      }
      return null;
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Error getting remember me credentials: $e');
      }
      return null;
    }
  }

  /// Check if "Remember me" is enabled
  static Future<bool> isRememberMeEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('remember_me_enabled') ?? false;
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Error checking remember me status: $e');
      }
      return false;
    }
  }

  /// Clear "Remember me" credentials
  static Future<void> clearRememberMeCredentials() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('remember_me_email');
      await prefs.remove('remember_me_password');
      await prefs.setBool('remember_me_enabled', false);
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Error clearing remember me credentials: $e');
      }
    }
  }

  /// Verify a newly registered user account using OTP
  ///
  /// Takes the user's email/phone, OTP code, and user type
  /// Returns true if verification is successful, false otherwise
  static Future<bool> verifyNewUserAccount({
    required String id,
    required String otp,
    required String userType,
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl/v1/s/otp/newuser/').replace(
        queryParameters: {
          'id': id,
          'otp': otp,
          'userType': userType,
        },
      );

      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': '${ConfigService.appName}/${ConfigService.appVersion}',
        'x-request-referral': 'riideon', // Required header - must match API key
      };

      // Add authorization header based on Swagger "Authorize" field
      final apiKey = ConfigService.apiKey;
      if (apiKey.isNotEmpty && apiKey != 'dev_api_key_placeholder') {
        headers['Authorization'] = 'Bearer $apiKey';
      }

      if (ConfigService.enableDebugMode) {
        print('AuthService: Making verification request to $uri');
        print('AuthService: Headers: $headers');
      }

      final response = await http.get(
        uri,
        headers: headers,
      ).timeout(Duration(milliseconds: _timeout));

      if (ConfigService.enableDebugMode) {
        print('AuthService: Verification response status: ${response.statusCode}');
        print('AuthService: Verification response body: ${response.body}');
        print('AuthService: Response headers: ${response.headers}');
      }

      // Handle successful verification (200 status)
      if (response.statusCode == 200) {
        try {
          // Parse response to get account data
          final responseData = jsonDecode(response.body) as Map<String, dynamic>;

          // Check if we have account data and token
          if (responseData.containsKey('account') && responseData.containsKey('token')) {
            // Optionally store the verification token and account data
            // This allows the user to be automatically signed in after verification
            final token = responseData['token'] as String?;
            final accountData = responseData['account'] as Map<String, dynamic>?;

            if (token != null && accountData != null) {
              try {
                final account = Account.fromJson(accountData);
                // Store authentication data for automatic sign-in
                await _storeAuthData(token, account);
              } catch (e) {
                if (ConfigService.enableDebugMode) {
                  print('AuthService: Warning - Could not parse account data: $e');
                }
                // Continue anyway - verification was successful
              }
            }
          }

          return true;
        } catch (e) {
          if (ConfigService.enableDebugMode) {
            print('AuthService: Warning - Could not parse verification response: $e');
            print('AuthService: Response body: ${response.body}');
          }
          // Even if parsing fails, 200 status means verification was successful
          return true;
        }
      }
      // Handle verification errors
      else if (response.statusCode == 400) {
        if (ConfigService.enableDebugMode) {
          print('AuthService: Verification failed - Invalid ID format');
        }
        return false;
      }
      else if (response.statusCode == 404) {
        if (ConfigService.enableDebugMode) {
          print('AuthService: Verification failed - Code expired or invalid');
        }
        return false;
      }
      else {
        if (ConfigService.enableDebugMode) {
          print('AuthService: Verification failed - Server error ${response.statusCode}');
        }
        return false;
      }
    } on SocketException {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Verification failed - No internet connection');
      }
      return false;
    } on TimeoutException {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Verification failed - Request timeout');
      }
      return false;
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Verification failed - Unexpected error: $e');
      }
      return false;
    }
  }

  /// Resend OTP for account verification
  ///
  /// Uses the sign-up endpoint to trigger OTP resend
  /// According to API docs: "If a client user tries to re-register within a 30 minutes period,
  /// a verification code is resent to the user"
  static Future<bool> resendVerificationOTP({
    required String email,
    required String userType,
  }) async {
    try {
      // Use the sign-up endpoint to trigger resend
      // According to API docs, re-registering within 30 minutes resends the OTP
      final uri = Uri.parse('$_baseUrl/v1/client/sign-up');

      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': '${ConfigService.appName}/${ConfigService.appVersion}',
        'x-request-referral': 'riideon', // Required header - must match API key
      };

      // Add authorization header based on Swagger "Authorize" field
      final apiKey = ConfigService.apiKey;
      if (apiKey.isNotEmpty && apiKey != 'dev_api_key_placeholder') {
        headers['Authorization'] = 'Bearer $apiKey';
      }

      // Create a minimal sign-up request to trigger resend
      // We only need email to trigger the resend mechanism, but API validates all fields
      final requestBody = {
        'email': email,
        'password': 'TempPassword123!', // Valid password that meets API requirements
        'repeatedPassword': 'TempPassword123!', // Must match password
        'firstName': 'Resend', // Required field but not used for resend
        'lastName': 'Request', // Required field but not used for resend
        'phoneNumber': '09000000000', // Valid Nigerian phone format
      };

      if (ConfigService.enableDebugMode) {
        print('AuthService: Making resend OTP request to $uri');
        print('AuthService: Using sign-up endpoint to trigger OTP resend for $email');
      }

      final response = await http.post(
        uri,
        headers: headers,
        body: jsonEncode(requestBody),
      ).timeout(Duration(milliseconds: _timeout));

      if (ConfigService.enableDebugMode) {
        print('AuthService: Resend OTP response status: ${response.statusCode}');
        print('AuthService: Resend OTP response body: ${response.body}');
      }

      // According to API docs, status 302 means verification code is resent
      if (response.statusCode == 302) {
        return true; // OTP resent successfully
      }
      // Status 409 means account already exists
      else if (response.statusCode == 409) {
        // Check if the response indicates account is already verified
        if (response.body.toLowerCase().contains('already registered')) {
          if (ConfigService.enableDebugMode) {
            print('AuthService: Account already registered - may be verified');
          }
          // Return false to indicate account might be verified
          return false;
        }
        return true; // Account exists, OTP should be resent
      }
      // Status 201 means account created (shouldn't happen for existing accounts)
      else if (response.statusCode == 201) {
        return true; // Account created, OTP sent
      }
      else {
        if (ConfigService.enableDebugMode) {
          print('AuthService: Resend OTP failed - Server response ${response.statusCode}');
          print('AuthService: Response: ${response.body}');
        }
        return false;
      }
    } on SocketException {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Resend OTP failed - No internet connection');
      }
      return false;
    } on TimeoutException {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Resend OTP failed - Request timeout');
      }
      return false;
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Resend OTP failed - Unexpected error: $e');
      }
      return false;
    }
  }

  /// Validate email format
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Validate phone number format
  static bool isValidPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters for validation
    final digitsOnly = phoneNumber.replaceAll(RegExp(r'[\s\-\(\)]'), '');

    // Check for various valid formats:
    // Nigerian format: 0XXXXXXXXX (11 digits starting with 0)
    // International format: +234XXXXXXXXX (13 digits starting with +234)
    // International without +: 234XXXXXXXXX (12 digits starting with 234)
    // General international: +XXXXXXXXXXX (7-15 digits with +)

    // Nigerian local format (0XXXXXXXXX)
    if (RegExp(r'^0[789][01]\d{8}$').hasMatch(digitsOnly)) {
      return true;
    }

    // Nigerian international format (+234XXXXXXXXX)
    if (RegExp(r'^\+234[789][01]\d{8}$').hasMatch(digitsOnly)) {
      return true;
    }

    // Nigerian international without + (234XXXXXXXXX)
    if (RegExp(r'^234[789][01]\d{8}$').hasMatch(digitsOnly)) {
      return true;
    }

    // General international format (+XXXXXXXXXXX)
    if (RegExp(r'^\+[1-9]\d{6,14}$').hasMatch(digitsOnly)) {
      return true;
    }

    // General format for other countries (7-15 digits)
    if (RegExp(r'^[1-9]\d{6,14}$').hasMatch(digitsOnly)) {
      return true;
    }

    return false;
  }

  /// Validate password strength
  static bool isValidPassword(String password) {
    return password.length >= 6;
  }

  /// Check if authentication service is properly configured
  static bool get isConfigured {
    return ConfigService.hasValue('API_BASE_URL');
  }

  /// Get authentication service configuration info (for debugging)
  static Map<String, dynamic> getConfigInfo() {
    if (!ConfigService.isDevelopment) {
      throw Exception('Config info can only be accessed in development mode');
    }
    
    return {
      'baseUrl': _baseUrl,
      'timeout': _timeout,
      'environment': ConfigService.appEnvironment,
      'isConfigured': isConfigured,
    };
  }
}
